from fastapi import <PERSON>AP<PERSON>
from fastapi.security import OAuth2PasswordBearer
from app.routers import users
from fastapi.middleware.cors import CORSMiddleware
from app.routers import tenant
from app.core.config import ALLOWED_ORIGINS

app = FastAPI(
    title="Multi-Tenant Admin System",
    description="Admin system for managing tenants and users across different products",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,  # Allow frontend origin
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods (GET, POST, etc.)
    allow_headers=["*"],  # Allow all headers
)


app.include_router(users.router)
app.include_router(tenant.router)