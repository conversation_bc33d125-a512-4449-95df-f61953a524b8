from fastapi import <PERSON><PERSON><PERSON>
from app.routers import users
from fastapi.middleware.cors import CORSMiddleware
from app.routers import tenant
from app.core.config import ALLOWED_ORIGINS
from fastapi.middleware.cors import CORSMiddleware


app = FastAPI()


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,  # Allow frontend origin
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods (GET, POST, etc.)
    allow_headers=["*"],  # Allow all headers
)


app.include_router(users.router)
app.include_router(tenant.router)