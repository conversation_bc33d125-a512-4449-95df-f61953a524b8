from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager
from app.routers import users
from app.routers import tenant
from app.core.config_loader import load_config_from_db, get_allowed_origins

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Load configuration from database on startup
    await load_config_from_db()
    yield

app = FastAPI(
    title="Multi-Tenant Admin System",
    description="Admin system for managing tenants and users across different products",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware using database configuration
@app.middleware("http")
async def cors_middleware(request, call_next):
    response = await call_next(request)

    # Get allowed origins from database
    allowed_origins = await get_allowed_origins()
    origin = request.headers.get("origin")

    if origin in allowed_origins:
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Allow-Methods"] = "*"
        response.headers["Access-Control-Allow-Headers"] = "*"

    return response

app.include_router(users.router)
app.include_router(tenant.router)