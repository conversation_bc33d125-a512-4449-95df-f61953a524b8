#!/usr/bin/env python3
"""
Setup script for the admin database.
Creates default admin user and role/permission system.
"""

import os
import json
import asyncio
from pymongo import AsyncMongoClient
from dotenv import load_dotenv
from app.core.security import hash_password

# Load environment variables
load_dotenv()

async def get_admin_db():
    """Get admin database connection"""
    mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
    client = AsyncMongoClient(mongo_uri)
    return client["multi_tenant_admin"]

async def setup_roles_permissions():
    """Setup roles and permissions in the database"""
    admin_db = await get_admin_db()
    
    # Load roles and permissions from JSON file
    with open('app/data/roles_permissions.json', 'r') as f:
        roles_data = json.load(f)
    
    # Insert or update roles collection
    roles_collection = admin_db.roles
    
    # Clear existing roles
    await roles_collection.delete_many({})

    # Insert roles
    for role_name, role_data in roles_data["roles"].items():
        role_doc = {
            "name": role_name,
            "level": role_data["level"],
            "display_name": role_data["name"],
            "description": role_data["description"],
            "permissions": role_data["permissions"]
        }
        await roles_collection.insert_one(role_doc)
        print(f"✓ Created role: {role_name}")

    # Insert or update permissions collection
    permissions_collection = admin_db.permissions

    # Clear existing permissions
    await permissions_collection.delete_many({})

    # Insert permissions
    for perm_name, perm_data in roles_data["permissions"].items():
        perm_doc = {
            "name": perm_name,
            "display_name": perm_data["name"],
            "description": perm_data["description"]
        }
        await permissions_collection.insert_one(perm_doc)
        print(f"✓ Created permission: {perm_name}")

async def setup_admin_user():
    """Create default admin user"""
    admin_db = await get_admin_db()
    users_collection = admin_db.users

    # Check if admin user already exists
    existing_admin = await users_collection.find_one({"username": "admin"})
    if existing_admin:
        print("⚠ Admin user already exists, skipping creation")
        return

    # Create admin user
    admin_user = {
        "username": "admin",
        "role": "admin",
        "hashed_password": hash_password("admin"),
        "created_at": "2024-01-01T00:00:00Z",
        "is_active": True,
        "is_system_admin": True
    }

    result = await users_collection.insert_one(admin_user)
    print(f"✓ Created admin user with ID: {result.inserted_id}")
    print("  Username: admin")
    print("  Password: admin")
    print("  Role: admin")

def setup_secrets():
    """Setup system secrets"""
    admin_db = get_admin_db()
    secrets_collection = admin_db.secrets
    
    # Check if secrets already exist
    existing_secrets = secrets_collection.find_one()
    if existing_secrets:
        print("⚠ Secrets already exist, skipping creation")
        return
    
    # Create default secrets
    secrets_doc = {
        "secret_key": os.getenv("SECRET_KEY", "default-secret-key"),
        "created_at": "2024-01-01T00:00:00Z"
    }
    
    result = secrets_collection.insert_one(secrets_doc)
    print(f"✓ Created secrets with ID: {result.inserted_id}")

def create_indexes():
    """Create database indexes for better performance"""
    admin_db = get_admin_db()
    
    # Create indexes
    admin_db.users.create_index("username", unique=True)
    admin_db.tenants.create_index("slug", unique=True)
    admin_db.roles.create_index("name", unique=True)
    admin_db.permissions.create_index("name", unique=True)
    
    print("✓ Created database indexes")

def main():
    """Main setup function"""
    print("🚀 Setting up admin database...")
    print(f"📍 MongoDB URI: {os.getenv('MONGO_URI', 'mongodb://localhost:27017/')}")
    print(f"📍 Database: multi_tenant_admin")
    print()
    
    try:
        # Test database connection
        admin_db = get_admin_db()
        admin_db.command('ping')
        print("✓ Database connection successful")
        
        # Setup components
        setup_roles_permissions()
        setup_admin_user()
        setup_secrets()
        create_indexes()
        
        print()
        print("🎉 Admin database setup completed successfully!")
        print()
        print("📋 Summary:")
        print("  - Roles: admin, user")
        print("  - Permissions: user.create, user.read, tenant.create, tenant.read")
        print("  - Default admin user created (username: admin, password: admin)")
        print("  - Database indexes created")
        print()
        print("⚠ IMPORTANT: Change the default admin password in production!")
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
