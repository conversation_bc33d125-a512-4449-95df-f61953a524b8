from fastapi import APIRouter, HTTPException
from app.core.database import get_db_from_tenant_id, get_admin_db
from pymongo.errors import DuplicateKeyError, CollectionInvalid
from passlib.context import CryptContext
tenant_name = "four semetrons"
tenant_slug = "fsem"

router = APIRouter(tags=["Tenants"])

@router.post("/tenants/create")
def create_tenant(tenant_name, tenant_slug):
    try:
        # Check if tenant with the same slug already exists
        if get_admin_db().tenants.find_one({"slug": tenant_slug}):
            raise ValueError(f"A tenant with slug '{tenant_slug}' already exists.")

        # Create tenant entry in the admin database
        tenant_id = get_admin_db().tenants.insert_one({
            "name": tenant_name,
            "slug": tenant_slug,
            "database_name": tenant_slug + "_db"
        }).inserted_id
        print("Tenant created successfully with ID:", tenant_id)

        # Define collections to create
        collections = ["users", "invitations", "jobs", "projects","settings","activity_logs"]

        # Retrieve secrets for creating the admin user
        secrets = get_admin_db().secrets.find_one()
        if not secrets:
            raise ValueError("No secrets found in admin database.")

        # Create collections for the tenant, only if they do not exist
        tenant_db = get_db_from_tenant_id(str(tenant_id))
        for collection in collections:
            try:
                tenant_db.create_collection(collection)
                print(f"Collection '{collection}' created.")
            except CollectionInvalid:
                print(f"Collection '{collection}' already exists. Skipping creation.")

        # Set up password hashing
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        hashed_password = pwd_context.hash(secrets['secret_key'])

        # Insert superadmin user, checking if it already exists
        if not tenant_db.users.find_one({"username": "superadmin"}):
            tenant_db.users.insert_one({
                "username": "superadmin",
                "role": "admin",
                "tenant_id": str(tenant_id),
                "hashed_password": hashed_password
            })
            print("Superadmin user created.")
            tenant_db.users.insert_one({
                "username": "test_supervisor",
                "role": "supervisor",
                "tenant_id": str(tenant_id),
                "hashed_password": hashed_password
            })
            tenant_db.users.insert_one({
                "username": "test_agent",
                "role": "agent",
                "tenant_id": str(tenant_id),
                "hashed_password": hashed_password
            })

        else:
            print("Superadmin user already exists. Skipping creation.")

        #insert settings
        settings_collection = tenant_db.settings
        settings_collection.insert_one({
            "name":"env",
            "config": {
                "GOOGLE_API_KEY": ""
            }
        })
        settings_collection.insert_one({

            "name": "model_config",
            "model_name": "gemini-1.5-flash",
            "generation_config": {
                "temperature": 0,
                "top_p": 0.95,
                "top_k": 64,
                "max_output_tokens": 8192,
                "response_mime_type": "application/json"
            },
            "system_instructions": "You are given an image consisting of a {type_}. Your task is to extract texts as specified. Provide output as a List of Dictionaries in JSON format.\n        {additinal_instructions}\n\n        Extract the following information:\n        {feature_instructions}\n        If the information is not present, provide an empty string.\n\n        Always follow the output format strictly.\n\n        Output format:\n        \n        [array of resulting json with key values of the extracted data]\n         \n"
        })
    except DuplicateKeyError:
        print("A tenant with this slug or database name already exists.")
    except ValueError as ve:
        print("Error:", ve)
    except Exception as e:
        print("Error creating tenant:", e)

create_tenant(tenant_name, tenant_slug)
