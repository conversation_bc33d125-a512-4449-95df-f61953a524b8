from fastapi import APIRouter, HTTPException, Depends
from app.core.database import get_db_from_tenant_id, get_admin_db
from pymongo.errors import Duplicate<PERSON>eyError, CollectionInvalid
from app.core.security import hash_password
from app.schemas.tenant import <PERSON>ant<PERSON><PERSON>, TenantResponse
from app.core.permissions import require_admin
from app.models.user import UserTenantDB

router = APIRouter(tags=["Tenants"])

@router.post("/tenants/create", response_model=TenantResponse)
async def create_tenant(
    tenant_data: TenantCreate,
    current_user: UserTenantDB = Depends(require_admin())
):
    """
    Create a new tenant with its own database and collections.
    """
    try:
        # Get admin database
        admin_db = await get_admin_db()

        # Check if tenant with the same slug already exists
        existing_tenant = await admin_db.tenants.find_one({"slug": tenant_data.slug})
        if existing_tenant:
            raise HTTPException(
                status_code=400,
                detail=f"A tenant with slug '{tenant_data.slug}' already exists."
            )

        # Create tenant entry in the admin database
        database_name = tenant_data.product + "_" + tenant_data.slug + "_db"
        result = await admin_db.tenants.insert_one({
            "name": tenant_data.name,
            "slug": tenant_data.slug,
            "database_name": database_name
        })
        tenant_id = result.inserted_id

        # Define collections to create
        collections = ["users", "invitations", "jobs", "projects","settings","activity_logs"]

        # Retrieve secrets for creating the admin user
        secrets = await admin_db.secrets.find_one()
        if not secrets:
            raise ValueError("No secrets found in admin database.")

        # Create collections for the tenant, only if they do not exist
        tenant_db = await get_db_from_tenant_id(str(tenant_id))
        for collection in collections:
            try:
                await tenant_db.create_collection(collection)
                print(f"Collection '{collection}' created.")
            except CollectionInvalid:
                print(f"Collection '{collection}' already exists. Skipping creation.")

        # Set up password hashing using Argon2
        hashed_password = hash_password(secrets['secret_key'])

        # Insert superadmin user, checking if it already exists
        existing_superadmin = await tenant_db.users.find_one({"username": "superadmin"})
        if not existing_superadmin:
            await tenant_db.users.insert_one({
                "username": "superadmin",
                "role": "admin",
                "tenant_id": str(tenant_id),
                "hashed_password": hashed_password
            })
            print("Superadmin user created.")
            await tenant_db.users.insert_one({
                "username": "test_supervisor",
                "role": "supervisor",
                "tenant_id": str(tenant_id),
                "hashed_password": hashed_password
            })
            await tenant_db.users.insert_one({
                "username": "test_agent",
                "role": "agent",
                "tenant_id": str(tenant_id),
                "hashed_password": hashed_password
            })

        else:
            print("Superadmin user already exists. Skipping creation.")

        #insert settings
        settings_collection = tenant_db.settings
        await settings_collection.insert_one({
            "name":"env",
            "config": {
                "GOOGLE_API_KEY": ""
            }
        })
        await settings_collection.insert_one({

            "name": "model_config",
            "model_name": "gemini-1.5-flash",
            "generation_config": {
                "temperature": 0,
                "top_p": 0.95,
                "top_k": 64,
                "max_output_tokens": 8192,
                "response_mime_type": "application/json"
            },
            "system_instructions": "You are given an image consisting of a {type_}. Your task is to extract texts as specified. Provide output as a List of Dictionaries in JSON format.\n        {additinal_instructions}\n\n        Extract the following information:\n        {feature_instructions}\n        If the information is not present, provide an empty string.\n\n        Always follow the output format strictly.\n\n        Output format:\n        \n        [array of resulting json with key values of the extracted data]\n         \n"
        })

        return TenantResponse(
            success=True,
            message="Tenant created successfully",
            tenant_id=str(tenant_id),
            tenant_name=tenant_data.name,
            tenant_slug=tenant_data.slug,
            database_name=database_name
        )

    except HTTPException:
        raise
    except DuplicateKeyError:
        raise HTTPException(
            status_code=400,
            detail="A tenant with this slug or database name already exists."
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error creating tenant: {str(e)}"
        )


@router.get("/tenants/list")
async def list_tenants(current_user: UserTenantDB = Depends(require_admin())):
    """
    List all tenants in the system.
    Only admins can view all tenants.
    """
    try:
        admin_db = await get_admin_db()
        cursor = admin_db.tenants.find({}, {
            "name": 1,
            "slug": 1,
            "database_name": 1,
            "_id": 1
        })
        tenants = await cursor.to_list(length=None)
        for tenant in tenants:
            tenant["_id"] = str(tenant["_id"])
        return {
            "success": True,
            "tenants": tenants,
            "total": len(tenants)
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching tenants: {str(e)}"
        )
