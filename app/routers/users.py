from fastapi import APIRouter, Depends, HTTPException, status, Request
from app.models.security import OAuth2PasswordRequestFormWithClientID
from bson.objectid import ObjectId
from app.utils import convert_objectid_to_str

from app.models.user import UserTenantDB


from app.core.security import (
    create_access_token, 
    verify_password, 
    create_invitation_token,
    verify_invitation_token,
    # get_current_user,
    user_tenant_info,
   min_role,
    pwd_context
)

from app.schemas.user import (
    UserCreate,
    UserDelete,
    UserLogin,
    AgentInvitation,
    AgentRegistration,
    AgentInvitationRecord
)

from datetime import timedelta, datetime

from app.core.database import get_db_from_tenant_id, get_tenant_id_from_slug


router = APIRouter(tags=["Users"])

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends()):    
    # find the database name of that tenant
    tenant_id = get_tenant_id_from_slug(form_data.client_id)
    tenant_database = get_db_from_tenant_id(tenant_id)
    # connect to the user_collection of that database. 
    user = tenant_database.users.find_one({"username": form_data.username})
    if not user or not verify_password(form_data.password, user["hashed_password"]):
        return {"access_token": None, "token_type": None, "username": None, "role": None}
    
    access_token = create_access_token(
        data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
        expires_delta=timedelta(minutes=120)
    )

    user = convert_objectid_to_str(user)
    
    return {
        "id": user["_id"],
        "access_token": access_token,
        "token_type": "bearer",
        "username": user['username'],
        "role": user['role'],
        "tenant_id": tenant_id
    }


@router.get("/verify_token")
async def verify_token(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_current_user will raise an exception
    """
    return True


