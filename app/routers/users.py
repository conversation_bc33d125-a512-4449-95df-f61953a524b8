from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from bson.objectid import ObjectId
from app.utils import convert_objectid_to_str

from app.models.user import UserTenantDB


from app.core.security import (
    create_access_token,
    verify_password,
    hash_password,
    create_invitation_token,
    verify_invitation_token,
    user_tenant_info
)

from app.core.permissions import (
    require_admin,
    
)

from app.schemas.user import (
    UserCreate,
    UserResponse
)

from datetime import timedelta, datetime

from app.core.database import get_admin_db


router = APIRouter(tags=["Users"])




@router.get("/verify_token")
async def verify_token(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_current_user will raise an exception
    """
    return True


@router.post("/create_user", response_model=UserResponse)
async def create_user(
    user_data: User<PERSON><PERSON>,
    current_user: UserTenantDB = Depends(require_admin())
):
    """
    Create a new user in the specified tenant.
    Only admins can create users.
    """
    try:
        # Get admin database (no tenant needed for admin system)
        admin_db = await get_admin_db()

        # Check if user already exists
        existing_user = await admin_db.users.find_one({"username": user_data.username})
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="User with this username already exists"
            )

        # Only allow creating admin, supervisor, or user roles in this admin system
        if user_data.role not in ["admin", "supervisor", "user"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid role. Only 'admin', 'supervisor', and 'user' roles are allowed in this system"
            )

        # Hash the password using Argon2
        hashed_password = hash_password(user_data.password)

        # Create user document
        user_doc = {
            "username": user_data.username,
            "role": user_data.role,
            "hashed_password": hashed_password,
            "created_at": datetime.utcnow(),
            "created_by": current_user.user.username,
            "is_active": True
        }

        # Insert user into admin database
        result = await admin_db.users.insert_one(user_doc)

        return UserResponse(
            success=True,
            message="User created successfully",
            user_id=str(result.inserted_id),
            username=user_data.username,
            role=user_data.role
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating user: {str(e)}")


@router.post("/auth/login")
async def authenticate_user(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    Authenticate user and return access token.
    This is for the admin database only - no tenant slug needed.
    """
    try:
        # Get admin database
        admin_db = await get_admin_db()

        # Find user in admin database
        user = await admin_db.users.find_one({"username": form_data.username})

        if not user or not verify_password(form_data.password, user["hashed_password"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Create access token (no tenant_id needed for admin system)
        access_token = await create_access_token(
            data={"sub": user["username"], "role": user["role"]},
            expires_delta=timedelta(minutes=120)
        )

        user = convert_objectid_to_str(user)

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user["_id"],
                "username": user['username'],
                "role": user['role']
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Authentication error: {str(e)}")


