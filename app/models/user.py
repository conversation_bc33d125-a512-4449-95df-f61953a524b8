from pydantic import BaseModel, Field, field_validator
from typing import Any, Literal
    

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "user"]

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

class UserTenantDB(BaseModel):
    tenant_id: str
    db: Any
    user: User