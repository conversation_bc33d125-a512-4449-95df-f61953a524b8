{"roles": {"admin": {"level": 100, "name": "Administrator", "description": "Full system access with all permissions", "permissions": ["user.create", "user.read", "user.update", "user.delete", "tenant.create", "tenant.read", "tenant.update", "tenant.delete", "project.create", "project.read", "project.update", "project.delete", "job.create", "job.read", "job.update", "job.delete", "job.assign", "job.unassign", "settings.read", "settings.update", "invitation.create", "invitation.read", "invitation.delete", "activity_logs.read", "system.manage"]}, "supervisor": {"level": 50, "name": "Supervisor", "description": "Manage projects, jobs, and agents", "permissions": ["user.read", "project.create", "project.read", "project.update", "project.delete", "job.create", "job.read", "job.update", "job.delete", "job.assign", "job.unassign", "invitation.create", "invitation.read", "activity_logs.read"]}, "agent": {"level": 10, "name": "Agent", "description": "Basic access to assigned jobs and projects", "permissions": ["job.read", "job.update", "project.read"]}}, "permissions": {"user.create": {"name": "Create Users", "description": "Create new users in the system"}, "user.read": {"name": "Read Users", "description": "View user information"}, "user.update": {"name": "Update Users", "description": "Modify user information"}, "user.delete": {"name": "Delete Users", "description": "Remove users from the system"}, "tenant.create": {"name": "Create Tenants", "description": "Create new tenant organizations"}, "tenant.read": {"name": "Read Tenants", "description": "View tenant information"}, "tenant.update": {"name": "Update Tenants", "description": "Modify tenant settings"}, "tenant.delete": {"name": "Delete Tenants", "description": "Remove tenant organizations"}, "project.create": {"name": "Create Projects", "description": "Create new projects"}, "project.read": {"name": "Read Projects", "description": "View project information"}, "project.update": {"name": "Update Projects", "description": "Modify project details"}, "project.delete": {"name": "Delete Projects", "description": "Remove projects"}, "job.create": {"name": "Create Jobs", "description": "Create new jobs"}, "job.read": {"name": "Read Jobs", "description": "View job information"}, "job.update": {"name": "Update Jobs", "description": "Modify job details"}, "job.delete": {"name": "Delete Jobs", "description": "Remove jobs"}, "job.assign": {"name": "Assign <PERSON>s", "description": "Assign jobs to users"}, "job.unassign": {"name": "Unassign Jobs", "description": "Remove job assignments"}, "settings.read": {"name": "Read Settings", "description": "View system settings"}, "settings.update": {"name": "Update Settings", "description": "Modify system settings"}, "invitation.create": {"name": "Create Invitations", "description": "Send user invitations"}, "invitation.read": {"name": "Read Invitations", "description": "View invitation status"}, "invitation.delete": {"name": "Delete Invitations", "description": "Cancel invitations"}, "activity_logs.read": {"name": "Read Activity Logs", "description": "View system activity logs"}, "system.manage": {"name": "System Management", "description": "Full system administration access"}}}