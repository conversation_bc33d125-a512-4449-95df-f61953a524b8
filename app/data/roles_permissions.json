{"roles": {"admin": {"level": 100, "name": "Administrator", "description": "Full system access - can create tenants and users", "permissions": ["user.create", "user.read", "tenant.create", "tenant.read"]}, "user": {"level": 10, "name": "User", "description": "Basic user - can only view tenants", "permissions": ["tenant.read"]}}, "permissions": {"user.create": {"name": "Create Users", "description": "Create new users in the admin system"}, "user.read": {"name": "Read Users", "description": "View user information"}, "tenant.create": {"name": "Create Tenants", "description": "Create new tenant organizations for different products"}, "tenant.read": {"name": "Read Tenants", "description": "View tenant information and list"}}}