# app/schemas/user.py

from typing import Literal
from pydantic import BaseModel, Field
from datetime import datetime

class User(BaseModel):
    username: str
    role: str

class UserCreate(User):
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class UserDelete(BaseModel):
    user_id: str
    handle_jobs: Literal["delete","assign","unassign"]

class AgentInvitation(BaseModel):
    username: str = Field(..., example="agent_username")
    role: str = Field(..., example="role")

class AgentRegistration(BaseModel):
    username: str = Field(..., example="agent_username") 
    role: str = Field(..., example="role") 
    password: str = Field(..., example="strongpassword123")
    token: str = Field(..., example="invitation_token_here")

class AgentInvitationRecord(BaseModel):
    username: str
    role: str
    token: str
    invited_by: str  # Username of the inviter (admin or supervisor)
    expires_at: datetime
    used: bool = False