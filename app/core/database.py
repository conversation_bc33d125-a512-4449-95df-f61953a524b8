from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId
import os
from fastapi import HTTPException

load_dotenv()

def get_admin_db():
    try:
        return MongoClient(os.getenv("MONGO_URI"))["multi_tenant_admin"]
    except Exception as e:
        raise Exception("Admin database not found")

def get_db_from_tenant_id(tenant_id:str):
    # print(tenant_id)
    try:
        tenant_database_name = get_admin_db().tenants.find_one({"_id": ObjectId(tenant_id)})["database_name"]
        # return tenant_database_name
        return MongoClient(os.getenv("MONGO_URI"))[tenant_database_name]
    except Exception as e:
        raise Exception("Tenant database not found")
    
def get_tenant_id_from_slug(slug:str):
    try:
        tenant_id = get_admin_db().tenants.find_one({"slug":slug})['_id']
        return str(tenant_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail="Tenant with this slug not found")