# app/core/security.py

from datetime import datetime, timedelta
from argon2 import <PERSON><PERSON><PERSON>asher
from argon2.exceptions import VerifyMismatchError
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from fastapi import Depends, HTTPException

import jwt
from app.core.config import SECRET_KEY, ALG<PERSON><PERSON>HM#, ACCESS_TOKEN_EXPIRE_MINUTES
from app.models.user import UserTenantDB, User
from app.core.database import get_db_from_tenant_id
from typing import Optional


# Use Argon2 for password hashing (more secure than bcrypt)
pwd_hasher = PasswordHasher()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

def create_access_token(data: dict, expires_delta: timedelta = None):
    # print(data)
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=120)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception

    tenant_db = await get_db_from_tenant_id(payload.get("tenant_id"))
    user = await tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception
    return user

async def user_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        # print("payload: ", payload)
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception
    
    tenant_db = get_db_from_tenant_id(payload.get("tenant_id"))
    user = tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception
    
    return UserTenantDB(tenant_id=payload.get("tenant_id"), db=tenant_db, user=User(**user))


# min_role function moved to app.core.permissions for better organization

def hash_password(plain_password: str) -> str:
    """
    Hash a plain password using Argon2.
    """
    return pwd_hasher.hash(plain_password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against an Argon2 hashed password.
    """
    try:
        pwd_hasher.verify(hashed_password, plain_password)
        return True
    except VerifyMismatchError:
        return False

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")
        
        return username, invited_by, role
    
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")





