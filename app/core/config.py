import os
from dotenv import load_dotenv
load_dotenv()

# Database settings
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
DATABASE_NAME = os.getenv("DATABASE_NAME", "multi_tenant_db")


# # JWT settings
SECRET_KEY = os.getenv("SECRET_KEY", "godmod")
ALGORITHM = "HS256"

# ACCESS_TOKEN_EXPIRE_MINUTES = 120  # Token expiration time

# # API Keys
# OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
# GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")

# # Upload directory
# UPLOAD_DIR = os.getenv("UPLOAD_DIR", "uploads")

# # #AG Data API
# # AG_API = os.getenv('AG_API')

# # CORS settings
ALLOWED_ORIGINS = ["http://localhost:3000", "http://localhost:8404","http://localhost:6970","https://multitenant.nextai.asia","http://************:8404","http://************:6970"]

# # #Response Codes
SUCESS_RESPONSES = [200,201]