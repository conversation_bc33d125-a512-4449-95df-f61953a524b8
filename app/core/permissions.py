# app/core/permissions.py

import json
import os
from typing import List, Dict, Any
from fastapi import HTTPException, Depends
from app.models.user import UserTenantDB
from app.core.security import user_tenant_info

# Load roles and permissions from JSON file
def load_roles_permissions() -> Dict[str, Any]:
    """Load roles and permissions from JSON file"""
    json_path = os.path.join(os.path.dirname(__file__), "..", "data", "roles_permissions.json")
    with open(json_path, 'r') as f:
        return json.load(f)

# Cache the roles and permissions data
ROLES_PERMISSIONS = load_roles_permissions()

class PermissionChecker:
    """Class to handle permission checking logic"""
    
    @staticmethod
    def get_role_level(role: str) -> int:
        """Get the level of a role"""
        return ROLES_PERMISSIONS["roles"].get(role, {}).get("level", 0)
    
    @staticmethod
    def get_role_permissions(role: str) -> List[str]:
        """Get all permissions for a role"""
        return ROLES_PERMISSIONS["roles"].get(role, {}).get("permissions", [])
    
    @staticmethod
    def has_permission(user_role: str, required_permission: str) -> bool:
        """Check if a user role has a specific permission"""
        role_permissions = PermissionChecker.get_role_permissions(user_role)
        return required_permission in role_permissions
    
    @staticmethod
    def has_min_role_level(user_role: str, min_level: int) -> bool:
        """Check if user role meets minimum level requirement"""
        user_level = PermissionChecker.get_role_level(user_role)
        return user_level >= min_level
    
    @staticmethod
    def can_manage_role(manager_role: str, target_role: str) -> bool:
        """Check if a manager can manage a target role (must be higher level)"""
        manager_level = PermissionChecker.get_role_level(manager_role)
        target_level = PermissionChecker.get_role_level(target_role)
        return manager_level > target_level

def require_permission(permission: str):
    """Decorator to require a specific permission"""
    async def permission_dependency(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
        if not PermissionChecker.has_permission(user_tenant_info.user.role, permission):
            raise HTTPException(
                status_code=403,
                detail=f"Permission denied. Required permission: {permission}"
            )
        return user_tenant_info
    return permission_dependency

def require_role_level(min_level: int):
    """Decorator to require a minimum role level"""
    async def role_level_dependency(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
        if not PermissionChecker.has_min_role_level(user_tenant_info.user.role, min_level):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Minimum role level required: {min_level}"
            )
        return user_tenant_info
    return role_level_dependency

def require_role(allowed_roles: List[str]):
    """Decorator to require one of the specified roles"""
    async def role_dependency(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
        if user_tenant_info.user.role not in allowed_roles:
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required roles: {', '.join(allowed_roles)}"
            )
        return user_tenant_info
    return role_dependency

def require_admin():
    """Decorator to require admin role"""
    return require_role(["admin"])

def require_admin():
    """Decorator to require admin role - only admins can create users and tenants"""
    return require_role(["admin"])
