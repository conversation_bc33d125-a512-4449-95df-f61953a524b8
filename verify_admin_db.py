#!/usr/bin/env python3
"""
Verification script for the admin database.
Shows the current state of roles, permissions, and users.
"""

import os
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_admin_db():
    """Get admin database connection"""
    mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
    client = MongoClient(mongo_uri)
    return client["multi_tenant_admin"]

def verify_setup():
    """Verify the admin database setup"""
    print("🔍 Verifying admin database setup...")
    print()
    
    admin_db = get_admin_db()
    
    # Check roles
    print("📋 ROLES:")
    roles = list(admin_db.roles.find({}, {"_id": 0}))
    for role in roles:
        print(f"  • {role['name']} (Level: {role['level']})")
        print(f"    Description: {role['description']}")
        print(f"    Permissions: {', '.join(role['permissions'])}")
        print()
    
    # Check permissions
    print("🔐 PERMISSIONS:")
    permissions = list(admin_db.permissions.find({}, {"_id": 0}))
    for perm in permissions:
        print(f"  • {perm['name']}: {perm['display_name']}")
        print(f"    Description: {perm['description']}")
        print()
    
    # Check users
    print("👥 USERS:")
    users = list(admin_db.users.find({}, {"_id": 0, "hashed_password": 0}))
    for user in users:
        print(f"  • Username: {user['username']}")
        print(f"    Role: {user['role']}")
        print(f"    Active: {user.get('is_active', 'N/A')}")
        print(f"    System Admin: {user.get('is_system_admin', 'N/A')}")
        print()
    
    # Check tenants
    print("🏢 TENANTS:")
    tenants = list(admin_db.tenants.find({}, {"_id": 0}))
    if tenants:
        for tenant in tenants:
            print(f"  • Name: {tenant['name']}")
            print(f"    Slug: {tenant['slug']}")
            print(f"    Database: {tenant['database_name']}")
            print()
    else:
        print("  No tenants created yet.")
        print()
    
    # Check collections
    print("📊 DATABASE COLLECTIONS:")
    collections = admin_db.list_collection_names()
    for collection in collections:
        count = admin_db[collection].count_documents({})
        print(f"  • {collection}: {count} documents")
    print()
    
    print("✅ Verification completed!")

if __name__ == "__main__":
    verify_setup()
